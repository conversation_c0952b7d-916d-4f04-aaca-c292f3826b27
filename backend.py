from typing import List
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain_groq import ChatGroq
from dotenv import load_dotenv
from langchain_chroma import Chroma
from langchain_huggingface import HuggingFaceEmbeddings
from langchain.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain
from langchain_core.output_parsers import StrOutputParser
import os

# Load environment variables
load_dotenv()

# Initialize LLM
llm = ChatGroq(model="openai/gpt-oss-20b")

# Initialize embeddings
embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/all-mpnet-base-v2")

# Load and process documents
def setup_vector_store():
    # Load documents
    loader = PyPDFLoader("document/budget_union.pdf")
    documents = loader.load()
    
    # Split documents
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=200
    )
    splits = text_splitter.split_documents(documents)
    
    # Create vector store
    vector_store = Chroma.from_documents(
        documents=splits,
        embedding=embeddings,
        persist_directory="./chroma_langchain_db"
    )
    
    return vector_store

# Initialize vector store
vector_store = setup_vector_store()
retriever = vector_store.as_retriever(search_kwargs={"k": 3})

# Define the state structure
class GraphState(TypedDict):
    question: str
    documents: List
    generation: str

# Helper functions
def retrieve_from_vector_store(question: str):
    """Retrieve documents from vector store"""
    docs = retriever.invoke(question)
    return docs

def is_relevant(doc, question: str) -> bool:
    """Grade document relevance"""
    # Simple relevance check - you can make this more sophisticated
    prompt = PromptTemplate(
        template="""You are a grader assessing relevance of a retrieved document to a user question.
        
        Retrieved document: {document}
        
        User question: {question}
        
        If the document contains information related to the question, grade it as relevant.
        Give a binary score 'yes' or 'no' to indicate whether the document is relevant to the question.
        
        Provide the binary score as a JSON with a single key 'score' and no preamble or explanation.
        """,
        input_variables=["question", "document"],
    )
    
    chain = prompt | llm | StrOutputParser()
    result = chain.invoke({"question": question, "document": doc.page_content})
    
    try:
        import json
        score = json.loads(result)
        return score.get("score", "no").lower() == "yes"
    except:
        return True  # Default to relevant if parsing fails

def rewriter(question: str) -> str:
    """Rewrite question for better retrieval"""
    prompt = PromptTemplate(
        template="""You are a question re-writer that converts an input question to a better version that is optimized for vectorstore retrieval.
        
        Look at the input and try to reason about the underlying semantic intent / meaning.
        
        Here is the initial question:
        {question}
        
        Provide an improved question without any preamble.
        """,
        input_variables=["question"],
    )
    
    chain = prompt | llm | StrOutputParser()
    return chain.invoke({"question": question})

def is_grounded(generation: str, documents: List) -> bool:
    """Check if generation is grounded in documents"""
    prompt = PromptTemplate(
        template="""You are a grader assessing whether an answer is grounded in / supported by a set of retrieved facts.
        
        Retrieved facts: {documents}
        
        Answer: {generation}
        
        Give a binary score 'yes' or 'no' to indicate whether the answer is grounded in the retrieved facts.
        
        Provide the binary score as a JSON with a single key 'score' and no preamble or explanation.
        """,
        input_variables=["generation", "documents"],
    )
    
    chain = prompt | llm | StrOutputParser()
    docs_content = "\n\n".join([doc.page_content if hasattr(doc, 'page_content') else str(doc) for doc in documents])
    result = chain.invoke({"generation": generation, "documents": docs_content})
    
    try:
        import json
        score = json.loads(result)
        return score.get("score", "no").lower() == "yes"
    except:
        return True  # Default to grounded if parsing fails

def answers_question(generation: str, question: str) -> bool:
    """Check if generation answers the question"""
    prompt = PromptTemplate(
        template="""You are a grader assessing whether an answer addresses / resolves a question.
        
        User question: {question}
        
        Answer: {generation}
        
        Give a binary score 'yes' or 'no' to indicate whether the answer resolves the question.
        
        Provide the binary score as a JSON with a single key 'score' and no preamble or explanation.
        """,
        input_variables=["generation", "question"],
    )
    
    chain = prompt | llm | StrOutputParser()
    result = chain.invoke({"generation": generation, "question": question})
    
    try:
        import json
        score = json.loads(result)
        return score.get("score", "no").lower() == "yes"
    except:
        return True  # Default to useful if parsing fails

# Create RAG chain
def create_rag_chain():
    prompt = PromptTemplate(
        template="""You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question.
        If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise.
        
        Question: {question}
        
        Context: {context}
        
        Answer:
        """,
        input_variables=["question", "context"],
    )
    
    return prompt | llm | StrOutputParser()

rag_chain = create_rag_chain()

# Node functions
def retrieve(state: GraphState):
    """Retrieves from vector store"""
    print("---RETRIEVE---")
    question = state["question"]
    documents = retrieve_from_vector_store(question)
    return {"question": question, "documents": documents}

def grade_documents(state: GraphState):
    """Filters relevant docs"""
    print("---CHECK DOCUMENT RELEVANCE TO QUESTION---")
    question = state["question"]
    documents = state["documents"]
    
    filtered_docs = []
    for d in documents:
        if is_relevant(d, question):
            print("---GRADE: DOCUMENT RELEVANT---")
            filtered_docs.append(d)
        else:
            print("---GRADE: DOCUMENT NOT RELEVANT---")
    
    return {"question": question, "documents": filtered_docs}

def transform_query(state: GraphState):
    """Rewrites the query"""
    print("---TRANSFORM QUERY---")
    question = state["question"]
    better_question = rewriter(question)
    return {"question": better_question, "documents": state["documents"]}

def web_search(state: GraphState):
    """Web search using Tavily"""
    print("---WEB SEARCH---")
    question = state["question"]
    
    search_tool = TavilySearchResults(max_results=3)
    docs = search_tool.invoke({"query": question})
    
    # Convert search results to document-like objects
    web_results = []
    for doc in docs:
        # Create a simple object with page_content attribute
        class WebDoc:
            def __init__(self, content):
                self.page_content = content
        
        if isinstance(doc, dict):
            content = doc.get('content', str(doc))
        else:
            content = str(doc)
        
        web_results.append(WebDoc(content))
    
    return {"question": question, "documents": web_results}

def generate(state: GraphState):
    """Generate answer using LLM + context"""
    print("---GENERATE---")
    question = state["question"]
    documents = state["documents"]
    
    # Format context
    context = "\n\n".join([
        doc.page_content if hasattr(doc, 'page_content') else str(doc) 
        for doc in documents
    ])
    
    # Generate answer
    answer = rag_chain.invoke({"context": context, "question": question})
    
    return {"question": question, "documents": documents, "generation": answer}

def grade_generation(state: GraphState):
    """Check for hallucination and question relevance"""
    print("---CHECK HALLUCINATIONS---")
    question = state["question"]
    documents = state["documents"]
    generation = state["generation"]
    
    if not is_grounded(generation, documents):
        print("---DECISION: GENERATION IS NOT GROUNDED IN DOCUMENTS, RE-TRY---")
        return "not_grounded"
    
    print("---DECISION: GENERATION IS GROUNDED IN DOCUMENTS---")
    print("---GRADE GENERATION vs QUESTION---")
    
    if not answers_question(generation, question):
        print("---DECISION: GENERATION DOES NOT ADDRESS QUESTION, RE-TRY---")
        return "not_useful"
    
    print("---DECISION: GENERATION ADDRESSES QUESTION---")
    return "useful"

def decide_to_generate(state: GraphState):
    """Decide whether to generate or transform query"""
    print("---ASSESS GRADED DOCUMENTS---")
    
    if state["documents"]:
        print("---DECISION: GENERATE---")
        return "generate"
    else:
        print("---DECISION: ALL DOCUMENTS ARE NOT RELEVANT TO QUESTION, TRANSFORM QUERY---")
        return "transform_query"

# Build the graph
workflow = StateGraph(GraphState)

# Add nodes
workflow.add_node("retrieve", retrieve)
workflow.add_node("grade_documents", grade_documents)
workflow.add_node("transform_query", transform_query)
workflow.add_node("web_search", web_search)
workflow.add_node("generate", generate)

# Add edges
workflow.add_conditional_edges(
    START,
    lambda s: "retrieve",
    {"retrieve": "retrieve"}
)

workflow.add_edge("retrieve", "grade_documents")

workflow.add_conditional_edges(
    "grade_documents",
    decide_to_generate,
    {"generate": "generate", "transform_query": "transform_query"}
)

workflow.add_edge("transform_query", "web_search")
workflow.add_edge("web_search", "generate")

workflow.add_conditional_edges(
    "generate",
    grade_generation,
    {
        "not_grounded": "web_search",
        "not_useful": "transform_query",
        "useful": END,
    },
)

# Compile the graph
app = workflow.compile()
app

# Test the system
if __name__ == "__main__":
    # Test with a question about your document
    result = app.invoke({
        "question": "What are the key tax changes mentioned in the budget?"
    })
    
    print("\n" + "="*50)
    print("FINAL RESULT:")
    print("="*50)
    print(f"Question: {result['question']}")
    print(f"Answer: {result['generation']}")